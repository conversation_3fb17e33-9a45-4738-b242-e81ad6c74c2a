cmake_minimum_required(VERSION 3.10)
project(ModbusProtocolService VERSION 1.0.0 LANGUAGES CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置编译选项
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找依赖库
find_package(PkgConfig REQUIRED)

# 查找 libmodbus
# 首先尝试使用本地安装的版本
find_path(LIBMODBUS_INCLUDE_DIR modbus/modbus.h
    PATHS ${CMAKE_SOURCE_DIR}/libmodbus_install/include
          ${CMAKE_SOURCE_DIR}/libModbus-3.1.6-master/src
          /usr/include
          /usr/local/include
)

find_library(LIBMODBUS_LIBRARY
    NAMES modbus libmodbus
    PATHS ${CMAKE_SOURCE_DIR}/libmodbus_install/lib
          ${CMAKE_SOURCE_DIR}/libModbus-3.1.6-master/src/.libs
          /usr/lib
          /usr/local/lib
)

if(LIBMODBUS_INCLUDE_DIR AND LIBMODBUS_LIBRARY)
    set(LIBMODBUS_FOUND TRUE)
    set(LIBMODBUS_INCLUDE_DIRS ${LIBMODBUS_INCLUDE_DIR})
    set(LIBMODBUS_LIBRARIES ${LIBMODBUS_LIBRARY})
    message(STATUS "Found libmodbus: ${LIBMODBUS_LIBRARY}")
else()
    message(FATAL_ERROR "libmodbus not found. Please install libmodbus or build it from source.")
endif()

# 查找 hiredis (Redis 客户端库)
find_path(HIREDIS_INCLUDE_DIR hiredis/hiredis.h
    PATHS /usr/include
          /usr/local/include
          /opt/local/include
)

find_library(HIREDIS_LIBRARY
    NAMES hiredis
    PATHS /usr/lib/x86_64-linux-gnu
          /usr/lib
          /usr/local/lib
          /opt/local/lib
)

if(HIREDIS_INCLUDE_DIR AND HIREDIS_LIBRARY)
    set(HIREDIS_FOUND TRUE)
    set(HIREDIS_INCLUDE_DIRS ${HIREDIS_INCLUDE_DIR})
    set(HIREDIS_LIBRARIES ${HIREDIS_LIBRARY})
    message(STATUS "Found hiredis: ${HIREDIS_LIBRARY}")
    add_definitions(-DHAVE_HIREDIS)
else()
    set(HIREDIS_FOUND FALSE)
    message(STATUS "hiredis not found. Redis functionality will be disabled.")
endif()

# 查找线程库
find_package(Threads REQUIRED)

# 包含目录
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${LIBMODBUS_INCLUDE_DIRS})
if(HIREDIS_FOUND)
    include_directories(${HIREDIS_INCLUDE_DIRS})
    add_definitions(-DHAVE_HIREDIS)
endif()

# 源文件
set(MODBUS_SOURCES
    # 类型定义
    src/types/modbus_types.h

    # 工具类
    src/utils/logger.h
    src/utils/logger.cpp
    src/utils/thread_pool.h
    src/utils/thread_pool.cpp
    src/utils/utils.h
    src/utils/utils.cpp

    # 通信层
    src/comm/modbus_comm_interface.h
    src/comm/modbus_comm_base.cpp
    src/comm/modbus_rtu_comm.h
    src/comm/modbus_rtu_comm.cpp
    src/comm/modbus_tcp_comm.h
    src/comm/modbus_tcp_comm.cpp

    # 配置管理
    src/config/config_manager.h
    src/config/config_manager.cpp
    src/config/xml_config_parser.h
    src/config/xml_config_parser.cpp
    src/config/point_table_loader.h
    src/config/point_table_loader.cpp
    src/config/modbus_point_parser.h
    src/config/modbus_point_parser.cpp

    # Redis 集成
    src/redis/redis_publisher.h
    src/redis/redis_publisher.cpp
    src/redis/redis_subscriber.h
    src/redis/redis_subscriber.cpp
    src/redis/redis_manager.h
    src/redis/redis_manager.cpp
    src/redis/redis_message_handler.h
    src/redis/redis_message_handler.cpp

    # 数据管理
    src/data/data_point_manager.h
    src/data/data_point_manager.cpp

    # 设备管理
    src/device/device_manager.h
    src/device/device_manager.cpp

    # 服务层
    src/service/modbus_service.h
    src/service/modbus_service.cpp
)

# Redis 源文件已经包含在上面的 MODBUS_SOURCES 中

# 创建静态库
add_library(modbus_protocol_static STATIC ${MODBUS_SOURCES})

# 包含目录
target_include_directories(modbus_protocol_static PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${LIBMODBUS_INCLUDE_DIRS}
)

if(HIREDIS_FOUND)
    target_include_directories(modbus_protocol_static PUBLIC ${HIREDIS_INCLUDE_DIRS})
endif()

target_link_libraries(modbus_protocol_static
    ${LIBMODBUS_LIBRARIES}
    Threads::Threads
)

if(HIREDIS_FOUND)
    target_link_libraries(modbus_protocol_static ${HIREDIS_LIBRARIES})
endif()

# 创建主程序可执行文件
add_executable(modbus_service src/main.cpp)
target_link_libraries(modbus_service modbus_protocol_static)

# 创建动态库
add_library(modbus_protocol_shared SHARED ${MODBUS_SOURCES})
target_link_libraries(modbus_protocol_shared 
    ${LIBMODBUS_LIBRARIES}
    Threads::Threads
)

if(HIREDIS_FOUND)
    target_link_libraries(modbus_protocol_shared ${HIREDIS_LIBRARIES})
endif()

# 设置库的输出名称
set_target_properties(modbus_protocol_static PROPERTIES OUTPUT_NAME modbus_protocol)
set_target_properties(modbus_protocol_shared PROPERTIES OUTPUT_NAME modbus_protocol)

# 主程序 (将在后续实现)
# add_executable(modbus_service src/main.cpp)
# target_link_libraries(modbus_service modbus_protocol_static)

# 测试程序
option(BUILD_TESTS "Build test programs" ON)
if(BUILD_TESTS)
    enable_testing()
    
    # 简单的测试程序
    add_executable(test_logger src/tests/test_logger.cpp)
    target_link_libraries(test_logger modbus_protocol_static)
    
    add_executable(test_thread_pool src/tests/test_thread_pool.cpp)
    target_link_libraries(test_thread_pool modbus_protocol_static)
    
    # 如果有 libmodbus，添加通信测试
    if(LIBMODBUS_FOUND)
        add_executable(test_rtu_comm src/tests/test_rtu_comm.cpp)
        target_link_libraries(test_rtu_comm modbus_protocol_static)

        add_executable(test_tcp_comm src/tests/test_tcp_comm.cpp)
        target_link_libraries(test_tcp_comm modbus_protocol_static)
    endif()
    
    # 添加测试
    add_test(NAME logger_test COMMAND test_logger)
    add_test(NAME thread_pool_test COMMAND test_thread_pool)
    if(LIBMODBUS_FOUND)
        add_test(NAME rtu_comm_test COMMAND test_rtu_comm)
        add_test(NAME tcp_comm_test COMMAND test_tcp_comm)
    endif()
endif()

# 示例程序
option(BUILD_EXAMPLES "Build example programs" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(src/examples)
endif()

# 安装规则
install(TARGETS modbus_protocol_static modbus_protocol_shared
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# 安装头文件
install(DIRECTORY src/
    DESTINATION include/modbus_protocol
    FILES_MATCHING PATTERN "*.h"
)

# 安装配置文件
install(FILES 
    src/examples/modbus_config.ini
    src/examples/points.ini
    DESTINATION etc/modbus_protocol
    OPTIONAL
)

# 创建 pkg-config 文件
configure_file(
    ${CMAKE_SOURCE_DIR}/modbus_protocol.pc.in
    ${CMAKE_BINARY_DIR}/modbus_protocol.pc
    @ONLY
)

install(FILES ${CMAKE_BINARY_DIR}/modbus_protocol.pc
    DESTINATION lib/pkgconfig
)

# 打印配置信息
message(STATUS "")
message(STATUS "Configuration Summary:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  libmodbus: ${LIBMODBUS_FOUND}")
if(LIBMODBUS_FOUND)
    message(STATUS "    Include: ${LIBMODBUS_INCLUDE_DIRS}")
    message(STATUS "    Library: ${LIBMODBUS_LIBRARIES}")
endif()
message(STATUS "  hiredis: ${HIREDIS_FOUND}")
if(HIREDIS_FOUND)
    message(STATUS "    Include: ${HIREDIS_INCLUDE_DIRS}")
    message(STATUS "    Library: ${HIREDIS_LIBRARIES}")
endif()
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  Build Examples: ${BUILD_EXAMPLES}")
message(STATUS "")

# 测试程序
# if(BUILD_TESTS)
#     add_subdirectory(tests)
# endif()

# 安装规则
install(TARGETS modbus_protocol_static modbus_protocol_shared
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY src/
    DESTINATION include/modbus_protocol
    FILES_MATCHING PATTERN "*.h"
)
