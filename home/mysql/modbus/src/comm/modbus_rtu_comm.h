#ifndef MODBUS_RTU_COMM_H
#define MODBUS_RTU_COMM_H

#include "modbus_comm_interface.h"
#include "../utils/thread_pool.h"
#include "../utils/utils.h"
#include <modbus/modbus.h>
#include <memory>
#include <atomic>

namespace modbus {

// RTU 通信实现类
class ModbusRtuComm : public ModbusCommBase {
public:
    explicit ModbusRtuComm(const RtuCommParam& param, int device_id = 0);
    virtual ~ModbusRtuComm();
    
    // 禁止拷贝和赋值
    ModbusRtuComm(const ModbusRtuComm&) = delete;
    ModbusRtuComm& operator=(const ModbusRtuComm&) = delete;
    
    // 实现基类接口
    Result<bool> Initialize() override;
    void Shutdown() override;
    Result<bool> Connect() override;
    void Disconnect() override;
    Result<bool> Reconnect() override;
    bool IsConnected() const override;
    Result<bool> SetSlaveId(int slave_id) override;
    void SetTimeout(int timeout_ms) override;
    
    // Modbus 功能实现
    Result<std::vector<bool>> ReadCoils(int start_addr, int count) override;
    Result<std::vector<bool>> ReadDiscreteInputs(int start_addr, int count) override;
    Result<std::vector<uint16_t>> ReadHoldingRegisters(int start_addr, int count) override;
    Result<std::vector<uint16_t>> ReadInputRegisters(int start_addr, int count) override;
    Result<bool> WriteSingleCoil(int addr, bool value) override;
    Result<bool> WriteSingleRegister(int addr, uint16_t value) override;
    Result<bool> WriteMultipleCoils(int start_addr, const std::vector<bool>& values) override;
    Result<bool> WriteMultipleRegisters(int start_addr, const std::vector<uint16_t>& values) override;
    
    // RTU 特有方法
    const RtuCommParam& GetRtuParam() const { return rtu_param_; }
    void SetRtuParam(const RtuCommParam& param);
    
    // 串口状态检查
    bool IsSerialPortOpen() const;
    
    // 获取串口信息
    std::string GetSerialPortInfo() const;
    
private:
    // 内部方法
    Result<bool> CreateModbusContext();
    void DestroyModbusContext();
    Result<bool> ConfigureSerialPort();
    Result<bool> TestConnection();
    
    // 错误处理
    ErrorCode MapModbusError(int modbus_errno) const;
    std::string GetModbusErrorString(int modbus_errno) const;
    
    // 数据读取的通用方法
    template<typename T>
    Result<std::vector<T>> ReadData(int start_addr, int count, 
                                   int (*read_func)(modbus_t*, int, int, T*));
    
    // 数据写入的通用方法
    Result<bool> WriteData(int start_addr, const std::vector<uint8_t>& data,
                          int (*write_func)(modbus_t*, int, int, const uint8_t*));
    
    Result<bool> WriteData(int start_addr, const std::vector<uint16_t>& data,
                          int (*write_func)(modbus_t*, int, int, const uint16_t*));
    
    // 连接管理
    void StartConnectionMonitor();
    void StopConnectionMonitor();
    void ConnectionMonitorThread();
    
    // 自动重连
    void StartAutoReconnect();
    void StopAutoReconnect();
    void AutoReconnectThread();
    
private:
    RtuCommParam rtu_param_;            // RTU 参数
    modbus_t* modbus_ctx_;              // libmodbus 上下文
    std::atomic<bool> is_connected_;    // 连接状态
    std::atomic<bool> is_initialized_;  // 初始化状态
    
    // 线程管理
    std::unique_ptr<std::thread> monitor_thread_;
    std::unique_ptr<std::thread> reconnect_thread_;
    std::atomic<bool> monitor_running_;
    std::atomic<bool> reconnect_running_;
    Event stop_event_;
    
    // 同步控制
    mutable MutexLock comm_mutex_;      // 通信互斥锁
    mutable MutexLock context_mutex_;   // 上下文互斥锁
    
    // 配置参数
    bool enable_auto_reconnect_;        // 启用自动重连
    int reconnect_interval_ms_;         // 重连间隔
    int max_reconnect_attempts_;        // 最大重连次数
    int connection_check_interval_ms_;  // 连接检查间隔
    
    // 统计信息
    std::atomic<int> reconnect_count_;  // 重连次数
    uint64_t last_activity_time_;       // 最后活动时间
};

// 模板方法实现
template<typename T>
Result<std::vector<T>> ModbusRtuComm::ReadData(int start_addr, int count, 
                                              int (*read_func)(modbus_t*, int, int, T*)) {
    if (!ValidateAddress(start_addr)) {
        return Result<std::vector<T>>(ErrorCode::INVALID_PARAM, "Invalid start address");
    }
    
    if (!ValidateCount(count, Constants::MAX_REGISTER_COUNT)) {
        return Result<std::vector<T>>(ErrorCode::INVALID_PARAM, "Invalid count");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<T>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    AutoLock lock(comm_mutex_);
    
    std::vector<T> data(count);
    int result = read_func(modbus_ctx_, start_addr, count, data.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        
        if (error_code == EBADF || error_code == ECONNRESET) {
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
            is_connected_ = false;
        }
        
        return Result<std::vector<T>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    last_activity_time_ = utils::TimeUtils::GetCurrentTimestamp();
    
    return Result<std::vector<T>>(data);
}

} // namespace modbus

#endif // MODBUS_RTU_COMM_H
