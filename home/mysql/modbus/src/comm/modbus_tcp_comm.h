#ifndef MODBUS_TCP_COMM_H
#define MODBUS_TCP_COMM_H

#include "modbus_comm_interface.h"
#include "../utils/thread_pool.h"
#include "../utils/utils.h"
#include <modbus/modbus.h>
#include <memory>
#include <atomic>
#include <map>

namespace modbus {

// TCP 连接信息
struct TcpConnectionInfo {
    std::string ip;
    int port;
    modbus_t* modbus_ctx;
    bool is_connected;
    uint64_t last_activity_time;
    int reconnect_count;
    
    TcpConnectionInfo() 
        : port(502), modbus_ctx(nullptr), is_connected(false), 
          last_activity_time(0), reconnect_count(0) {}
};

// TCP 通信实现类
class ModbusTcpComm : public ModbusCommBase {
public:
    explicit ModbusTcpComm(const TcpCommParam& param, int device_id = 0);
    virtual ~ModbusTcpComm();
    
    // 禁止拷贝和赋值
    ModbusTcpComm(const ModbusTcpComm&) = delete;
    ModbusTcpComm& operator=(const ModbusTcpComm&) = delete;
    
    // 实现基类接口
    Result<bool> Initialize() override;
    void Shutdown() override;
    Result<bool> Connect() override;
    void Disconnect() override;
    Result<bool> Reconnect() override;
    bool IsConnected() const override;
    Result<bool> SetSlaveId(int slave_id) override;
    void SetTimeout(int timeout_ms) override;
    
    // Modbus 功能实现
    Result<std::vector<bool>> ReadCoils(int start_addr, int count) override;
    Result<std::vector<bool>> ReadDiscreteInputs(int start_addr, int count) override;
    Result<std::vector<uint16_t>> ReadHoldingRegisters(int start_addr, int count) override;
    Result<std::vector<uint16_t>> ReadInputRegisters(int start_addr, int count) override;
    Result<bool> WriteSingleCoil(int addr, bool value) override;
    Result<bool> WriteSingleRegister(int addr, uint16_t value) override;
    Result<bool> WriteMultipleCoils(int start_addr, const std::vector<bool>& values) override;
    Result<bool> WriteMultipleRegisters(int start_addr, const std::vector<uint16_t>& values) override;
    
    // TCP 特有方法
    const TcpCommParam& GetTcpParam() const { return tcp_param_; }
    void SetTcpParam(const TcpCommParam& param);
    
    // 连接管理
    bool IsSocketConnected() const;
    std::string GetConnectionInfo() const;
    
    // 多连接支持
    Result<bool> AddConnection(const std::string& ip, int port);
    Result<bool> RemoveConnection(const std::string& ip, int port);
    Result<bool> SwitchConnection(const std::string& ip, int port);
    std::vector<std::string> GetActiveConnections() const;
    
    // 连接池管理
    void SetMaxConnections(int max_connections) { max_connections_ = max_connections; }
    int GetMaxConnections() const { return max_connections_; }
    int GetActiveConnectionCount() const;
    
private:
    // 内部方法
    Result<bool> CreateModbusContext();
    void DestroyModbusContext();
    Result<bool> ConfigureTcpConnection();
    Result<bool> TestConnection();
    
    // 连接管理
    std::string GetConnectionKey(const std::string& ip, int port) const;
    TcpConnectionInfo* GetCurrentConnection();
    const TcpConnectionInfo* GetCurrentConnection() const;
    
    // 错误处理
    ErrorCode MapModbusError(int modbus_errno) const;
    std::string GetModbusErrorString(int modbus_errno) const;
    
    // 数据读取的通用方法
    template<typename T>
    Result<std::vector<T>> ReadData(int start_addr, int count, 
                                   int (*read_func)(modbus_t*, int, int, T*));
    
    // 数据写入的通用方法
    Result<bool> WriteData(int start_addr, const std::vector<uint8_t>& data,
                          int (*write_func)(modbus_t*, int, int, const uint8_t*));
    
    Result<bool> WriteData(int start_addr, const std::vector<uint16_t>& data,
                          int (*write_func)(modbus_t*, int, int, const uint16_t*));
    
    // 连接监控
    void StartConnectionMonitor();
    void StopConnectionMonitor();
    void ConnectionMonitorThread();
    
    // 自动重连
    void StartAutoReconnect();
    void StopAutoReconnect();
    void AutoReconnectThread();
    
    // 连接池清理
    void CleanupInactiveConnections();
    
private:
    TcpCommParam tcp_param_;                    // TCP 参数
    std::string current_connection_key_;        // 当前连接键
    std::map<std::string, TcpConnectionInfo> connections_; // 连接池
    std::atomic<bool> is_connected_;            // 连接状态
    std::atomic<bool> is_initialized_;          // 初始化状态
    
    // 线程管理
    std::unique_ptr<std::thread> monitor_thread_;
    std::unique_ptr<std::thread> reconnect_thread_;
    std::atomic<bool> monitor_running_;
    std::atomic<bool> reconnect_running_;
    Event stop_event_;
    
    // 同步控制
    mutable MutexLock comm_mutex_;              // 通信互斥锁
    mutable MutexLock connections_mutex_;       // 连接池互斥锁
    
    // 配置参数
    bool enable_auto_reconnect_;                // 启用自动重连
    int reconnect_interval_ms_;                 // 重连间隔
    int max_reconnect_attempts_;                // 最大重连次数
    int connection_check_interval_ms_;          // 连接检查间隔
    int max_connections_;                       // 最大连接数
    int connection_timeout_ms_;                 // 连接超时时间
    
    // 统计信息
    std::atomic<int> total_reconnect_count_;    // 总重连次数
    uint64_t last_activity_time_;               // 最后活动时间
};

// 模板方法实现
template<typename T>
Result<std::vector<T>> ModbusTcpComm::ReadData(int start_addr, int count, 
                                              int (*read_func)(modbus_t*, int, int, T*)) {
    if (!ValidateAddress(start_addr)) {
        return Result<std::vector<T>>(ErrorCode::INVALID_PARAM, "Invalid start address");
    }
    
    if (!ValidateCount(count, Constants::MAX_REGISTER_COUNT)) {
        return Result<std::vector<T>>(ErrorCode::INVALID_PARAM, "Invalid count");
    }
    
    if (!IsConnected()) {
        auto connect_result = Connect();
        if (!connect_result.IsSuccess()) {
            return Result<std::vector<T>>(ErrorCode::COMM_ERROR, "Failed to connect");
        }
    }
    
    AutoLock lock(comm_mutex_);
    
    auto* conn = GetCurrentConnection();
    if (!conn || !conn->modbus_ctx) {
        return Result<std::vector<T>>(ErrorCode::COMM_ERROR, "No active connection");
    }
    
    std::vector<T> data(count);
    int result = read_func(conn->modbus_ctx, start_addr, count, data.data());
    
    if (result == -1) {
        int error_code = errno;
        RecordError(GetModbusErrorString(error_code));
        
        if (error_code == EBADF || error_code == ECONNRESET || error_code == EPIPE) {
            conn->is_connected = false;
            is_connected_ = false;
            UpdateConnectionStatus(DeviceStatus::DISCONNECTED);
        }
        
        return Result<std::vector<T>>(MapModbusError(error_code), GetModbusErrorString(error_code));
    }
    
    RecordSuccess();
    conn->last_activity_time = utils::TimeUtils::GetCurrentTimestamp();
    last_activity_time_ = conn->last_activity_time;
    
    return Result<std::vector<T>>(data);
}

} // namespace modbus

#endif // MODBUS_TCP_COMM_H
